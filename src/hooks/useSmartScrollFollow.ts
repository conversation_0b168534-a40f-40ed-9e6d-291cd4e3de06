import { useRef, useEffect, useCallback, useState } from 'react'

interface ScrollFollowOptions {
  enabled?: boolean
  threshold?: number // 距离底部多少像素时认为在底部
  smoothScroll?: boolean
  debounceMs?: number
}

interface ScrollFollowState {
  isUserScrolling: boolean
  isAtBottom: boolean
  shouldAutoFollow: boolean
  lastScrollTop: number
  lastScrollTime: number
}

export const useSmartScrollFollow = (options: ScrollFollowOptions = {}) => {
  const {
    enabled = true,
    threshold = 100,
    smoothScroll = true,
    debounceMs = 150
  } = options

  const containerRef = useRef<HTMLDivElement>(null)
  const scrollTimeoutRef = useRef<NodeJS.Timeout>()
  const isAutoScrollingRef = useRef(false)
  const lastContentHeightRef = useRef(0)

  const [scrollState, setScrollState] = useState<ScrollFollowState>({
    isUserScrolling: false,
    isAtBottom: true,
    shouldAutoFollow: true,
    lastScrollTop: 0,
    lastScrollTime: 0
  })

  // 检查是否在底部
  const checkIsAtBottom = useCallback((element: HTMLElement): boolean => {
    const { scrollTop, scrollHeight, clientHeight } = element
    return scrollHeight - scrollTop - clientHeight <= threshold
  }, [threshold])

  // 平滑滚动到底部
  const scrollToBottom = useCallback((force = false) => {
    const container = containerRef.current
    if (!container || (!enabled && !force)) return

    isAutoScrollingRef.current = true
    
    if (smoothScroll) {
      container.scrollTo({
        top: container.scrollHeight,
        behavior: 'smooth'
      })
    } else {
      container.scrollTop = container.scrollHeight
    }

    // 重置自动滚动标记
    setTimeout(() => {
      isAutoScrollingRef.current = false
    }, 500)
  }, [enabled, smoothScroll])

  // 处理滚动事件
  const handleScroll = useCallback(() => {
    const container = containerRef.current
    if (!container || isAutoScrollingRef.current) return

    const now = Date.now()
    const { scrollTop, scrollHeight, clientHeight } = container
    const isAtBottom = checkIsAtBottom(container)

    // 检测用户主动滚动
    const isUserScrolling = Math.abs(scrollTop - scrollState.lastScrollTop) > 5
    
    setScrollState(prev => {
      const newState = {
        ...prev,
        isUserScrolling,
        isAtBottom,
        shouldAutoFollow: isAtBottom, // 如果在底部，启用自动跟随
        lastScrollTop: scrollTop,
        lastScrollTime: now
      }
      return newState
    })

    // 清除之前的超时
    if (scrollTimeoutRef.current) {
      clearTimeout(scrollTimeoutRef.current)
    }

    // 设置防抖，在用户停止滚动后重新评估状态
    scrollTimeoutRef.current = setTimeout(() => {
      setScrollState(prev => ({
        ...prev,
        isUserScrolling: false
      }))
    }, debounceMs)

  }, [checkIsAtBottom, scrollState.lastScrollTop, debounceMs])

  // 内容变化时的自动滚动
  const handleContentChange = useCallback(() => {
    const container = containerRef.current
    if (!container || !enabled) return

    const currentHeight = container.scrollHeight
    const heightChanged = currentHeight !== lastContentHeightRef.current
    
    if (heightChanged && scrollState.shouldAutoFollow && !scrollState.isUserScrolling) {
      // 使用 requestAnimationFrame 确保 DOM 更新完成后再滚动
      requestAnimationFrame(() => {
        scrollToBottom()
      })
    }

    lastContentHeightRef.current = currentHeight
  }, [enabled, scrollState.shouldAutoFollow, scrollState.isUserScrolling, scrollToBottom])

  // 设置滚动监听
  useEffect(() => {
    const container = containerRef.current
    if (!container || !enabled) return

    container.addEventListener('scroll', handleScroll, { passive: true })
    
    return () => {
      container.removeEventListener('scroll', handleScroll)
      if (scrollTimeoutRef.current) {
        clearTimeout(scrollTimeoutRef.current)
      }
    }
  }, [handleScroll, enabled])

  // 监听内容变化
  useEffect(() => {
    const container = containerRef.current
    if (!container || !enabled) return

    const observer = new MutationObserver(() => {
      handleContentChange()
    })

    observer.observe(container, {
      childList: true,
      subtree: true,
      characterData: true
    })

    return () => {
      observer.disconnect()
    }
  }, [handleContentChange, enabled])

  // 强制滚动到底部
  const forceScrollToBottom = useCallback(() => {
    scrollToBottom(true)
    setScrollState(prev => ({
      ...prev,
      shouldAutoFollow: true,
      isAtBottom: true
    }))
  }, [scrollToBottom])

  // 暂停自动跟随
  const pauseAutoFollow = useCallback(() => {
    setScrollState(prev => ({
      ...prev,
      shouldAutoFollow: false
    }))
  }, [])

  // 恢复自动跟随
  const resumeAutoFollow = useCallback(() => {
    setScrollState(prev => ({
      ...prev,
      shouldAutoFollow: true
    }))
  }, [])

  return {
    containerRef,
    scrollState,
    forceScrollToBottom,
    pauseAutoFollow,
    resumeAutoFollow,
    scrollToBottom: () => scrollToBottom(false)
  }
}
