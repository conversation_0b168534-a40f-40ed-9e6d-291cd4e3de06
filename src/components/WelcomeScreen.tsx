'use client'

import { useState } from 'react'
import { Send, Paperclip, Brain } from 'lucide-react'

interface WelcomeScreenProps {
  onSubmit: (input: string) => void
  loading: boolean
}

export function WelcomeScreen({ onSubmit, loading }: WelcomeScreenProps) {
  const [input, setInput] = useState('')

  const handleSubmit = () => {
    if (input.trim()) {
      onSubmit(input.trim())
    }
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey && !loading) {
      e.preventDefault()
      handleSubmit()
    }
  }

  return (
    <div className="min-h-screen bg-white flex flex-col">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-100">
        <div className="flex items-center space-x-3">
          <div className="w-8 h-8 bg-black rounded-lg flex items-center justify-center">
            <Brain className="w-4 h-4 text-white" />
          </div>
          <span className="font-semibold text-gray-900">知识卡片</span>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col items-center justify-center px-6">
        <div className="w-full max-w-2xl">
          {/* Welcome Message */}
          <div className="text-center mb-8">
            <h1 className="text-4xl font-normal text-gray-900 mb-2">
              有什么可以帮助您的？
            </h1>
          </div>

          {/* Input Area */}
          <div className="relative">
            <div className="relative flex items-center bg-white border border-gray-300 rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200">
              <button
                type="button"
                className="p-3 text-gray-400 hover:text-gray-600 transition-colors duration-200"
                disabled={loading}
              >
                <Paperclip className="w-5 h-5" />
              </button>

              <textarea
                value={input}
                onChange={(e) => setInput(e.target.value)}
                onKeyDown={handleKeyDown}
                disabled={loading}
                placeholder="输入网页链接或文章内容..."
                className="flex-1 min-h-[50px] max-h-[200px] px-3 py-3 bg-transparent border-0 resize-none outline-none placeholder:text-gray-500 text-gray-900 text-base leading-relaxed"
                rows={1}
              />

              <button
                onClick={handleSubmit}
                disabled={!input.trim() || loading}
                className="p-3 text-gray-400 hover:text-gray-600 disabled:text-gray-300 disabled:cursor-not-allowed transition-colors duration-200"
              >
                {loading ? (
                  <div className="w-5 h-5 border-2 border-gray-300 border-t-gray-600 rounded-full animate-spin" />
                ) : (
                  <Send className="w-5 h-5" />
                )}
              </button>
            </div>
          </div>

        </div>
      </div>

      {/* Footer */}
      <div className="p-4 text-center border-t border-gray-100">
        <p className="text-sm text-gray-500">
          知识卡片可以生成错误信息。请核查重要信息。
        </p>
      </div>
    </div>
  )
}