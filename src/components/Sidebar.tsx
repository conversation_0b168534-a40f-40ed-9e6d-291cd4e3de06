'use client'

import { KnowledgeCards } from './KnowledgeCards'
import { AIChat } from './AIChat'

interface KnowledgeCard {
  title: string
  content: string
}

interface ChatMessage {
  role: 'user' | 'assistant'
  content: string
}

interface SidebarProps {
  knowledgeCards: KnowledgeCard[]
  knowledgeCardsLoading: boolean
  chatMessages: ChatMessage[]
  chatLoading: boolean
  onSaveCard: (card: KnowledgeCard) => Promise<void>
  onSendMessage: (message: string) => void
}

export function Sidebar({
  knowledgeCards,
  knowledgeCardsLoading,
  chatMessages,
  chatLoading,
  onSaveCard,
  onSendMessage,
}: SidebarProps) {
  return (
    <div className="h-full flex flex-col">
      {/* 知识卡片区域 */}
      <div className="flex-1 border-b border-border/50">
        <div className="p-6 border-b border-border/50 bg-gradient-to-r from-primary/5 to-purple-500/5 backdrop-blur-sm">
          <div className="flex items-center space-x-3 mb-2">
            <div className="w-8 h-8 bg-gradient-to-br from-primary to-purple-500 rounded-lg flex items-center justify-center">
              <span className="text-white text-sm">💡</span>
            </div>
            <div>
              <h3 className="text-sm font-bold text-foreground">知识卡片</h3>
              <p className="text-xs text-muted-foreground">
                {knowledgeCards.length} 张卡片
              </p>
            </div>
          </div>
          <div className="w-full bg-primary/10 rounded-full h-1.5">
            <div
              className="bg-gradient-to-r from-primary to-purple-500 h-1.5 rounded-full transition-all duration-500"
              style={{ width: `${Math.min(knowledgeCards.length * 20, 100)}%` }}
            ></div>
          </div>
        </div>
        <div className="flex-1 overflow-y-auto p-4 scrollbar-thin">
          <KnowledgeCards
            cards={knowledgeCards}
            loading={knowledgeCardsLoading}
            onSaveCard={onSaveCard}
          />
        </div>
      </div>

      {/* AI 聊天区域 */}
      <div className="h-96 flex flex-col">
        <div className="p-6 border-b border-border/50 bg-gradient-to-r from-emerald-500/5 to-teal-500/5 backdrop-blur-sm">
          <div className="flex items-center space-x-3 mb-2">
            <div className="w-8 h-8 bg-gradient-to-br from-emerald-500 to-teal-500 rounded-lg flex items-center justify-center">
              <span className="text-white text-sm">🤖</span>
            </div>
            <div>
              <h3 className="text-sm font-bold text-foreground">AI 助手</h3>
              <p className="text-xs text-muted-foreground">
                基于文章内容的智能问答
              </p>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
            <span className="text-xs font-medium text-green-600">在线</span>
          </div>
        </div>
        <div className="flex-1">
          <AIChat
            messages={chatMessages}
            loading={chatLoading}
            onSendMessage={onSendMessage}
          />
        </div>
      </div>
    </div>
  )
}