'use client'

import React, { useState, useEffect, useRef } from 'react'
import { Activity, Zap, Clock, AlertTriangle } from 'lucide-react'

interface StreamingMetrics {
  updateCount: number
  averageUpdateInterval: number
  lastUpdateTime: number
  totalStreamingTime: number
  renderTime: number
  memoryUsage?: number
}

interface StreamingPerformanceMonitorProps {
  isStreaming: boolean
  content: string
  onMetricsUpdate?: (metrics: StreamingMetrics) => void
  showMonitor?: boolean
}

const StreamingPerformanceMonitor: React.FC<StreamingPerformanceMonitorProps> = ({
  isStreaming,
  content,
  onMetricsUpdate,
  showMonitor = false
}) => {
  const [metrics, setMetrics] = useState<StreamingMetrics>({
    updateCount: 0,
    averageUpdateInterval: 0,
    lastUpdateTime: 0,
    totalStreamingTime: 0,
    renderTime: 0
  })
  
  const startTimeRef = useRef<number>(0)
  const lastContentRef = useRef<string>('')
  const updateTimesRef = useRef<number[]>([])
  const renderStartRef = useRef<number>(0)

  // 监控流式更新性能
  useEffect(() => {
    if (isStreaming && startTimeRef.current === 0) {
      startTimeRef.current = performance.now()
      updateTimesRef.current = []
    }

    if (!isStreaming && startTimeRef.current > 0) {
      // 流式结束，计算总时间
      const totalTime = performance.now() - startTimeRef.current
      setMetrics(prev => ({
        ...prev,
        totalStreamingTime: totalTime
      }))
      startTimeRef.current = 0
    }
  }, [isStreaming])

  // 监控内容更新
  useEffect(() => {
    if (content !== lastContentRef.current && isStreaming) {
      const now = performance.now()
      renderStartRef.current = now
      
      // 记录更新时间
      if (metrics.lastUpdateTime > 0) {
        const interval = now - metrics.lastUpdateTime
        updateTimesRef.current.push(interval)
        
        // 保持最近50次更新的记录
        if (updateTimesRef.current.length > 50) {
          updateTimesRef.current.shift()
        }
      }

      const newMetrics = {
        ...metrics,
        updateCount: metrics.updateCount + 1,
        lastUpdateTime: now,
        averageUpdateInterval: updateTimesRef.current.length > 0 
          ? updateTimesRef.current.reduce((a, b) => a + b, 0) / updateTimesRef.current.length 
          : 0
      }

      setMetrics(newMetrics)
      onMetricsUpdate?.(newMetrics)
      lastContentRef.current = content
    }
  }, [content, isStreaming, metrics.lastUpdateTime, metrics.updateCount, onMetricsUpdate])

  // 监控渲染性能
  useEffect(() => {
    if (renderStartRef.current > 0) {
      const renderTime = performance.now() - renderStartRef.current
      setMetrics(prev => ({
        ...prev,
        renderTime
      }))
      renderStartRef.current = 0
    }
  })

  // 获取性能状态
  const getPerformanceStatus = () => {
    const { averageUpdateInterval, renderTime } = metrics
    
    if (averageUpdateInterval < 50 && renderTime < 16) {
      return { status: 'excellent', color: 'text-green-600', icon: Zap, label: '优秀' }
    } else if (averageUpdateInterval < 100 && renderTime < 32) {
      return { status: 'good', color: 'text-blue-600', icon: Activity, label: '良好' }
    } else if (averageUpdateInterval < 200 && renderTime < 50) {
      return { status: 'fair', color: 'text-yellow-600', icon: Clock, label: '一般' }
    } else {
      return { status: 'poor', color: 'text-red-600', icon: AlertTriangle, label: '需优化' }
    }
  }

  const performanceStatus = getPerformanceStatus()
  const Icon = performanceStatus.icon

  if (!showMonitor) return null

  return (
    <div className="fixed bottom-4 right-4 bg-white border border-gray-200 rounded-lg shadow-lg p-3 z-50 min-w-64">
      <div className="flex items-center space-x-2 mb-2">
        <Icon size={16} className={performanceStatus.color} />
        <span className="text-sm font-medium text-gray-900">流式性能监控</span>
        <div className={`w-2 h-2 rounded-full ${
          isStreaming ? 'bg-green-500 animate-pulse' : 'bg-gray-400'
        }`}></div>
      </div>
      
      <div className="space-y-1 text-xs">
        <div className="flex justify-between">
          <span className="text-gray-600">更新次数:</span>
          <span className="font-mono text-blue-600">
            {metrics.updateCount}
          </span>
        </div>
        
        <div className="flex justify-between">
          <span className="text-gray-600">平均间隔:</span>
          <span className={`font-mono ${performanceStatus.color}`}>
            {metrics.averageUpdateInterval.toFixed(1)}ms
          </span>
        </div>
        
        <div className="flex justify-between">
          <span className="text-gray-600">渲染时间:</span>
          <span className={`font-mono ${performanceStatus.color}`}>
            {metrics.renderTime.toFixed(1)}ms
          </span>
        </div>
        
        {metrics.totalStreamingTime > 0 && (
          <div className="flex justify-between">
            <span className="text-gray-600">总时长:</span>
            <span className="font-mono text-green-600">
              {(metrics.totalStreamingTime / 1000).toFixed(1)}s
            </span>
          </div>
        )}
        
        <div className="pt-1 border-t border-gray-100">
          <div className="flex items-center space-x-1">
            <div className={`w-2 h-2 rounded-full ${
              performanceStatus.status === 'excellent' ? 'bg-green-500' :
              performanceStatus.status === 'good' ? 'bg-blue-500' :
              performanceStatus.status === 'fair' ? 'bg-yellow-500' : 'bg-red-500'
            }`}></div>
            <span className={`text-xs ${performanceStatus.color}`}>
              {performanceStatus.label}
            </span>
          </div>
        </div>
        
        {/* 性能建议 */}
        {performanceStatus.status === 'poor' && (
          <div className="pt-1 text-xs text-red-600">
            建议: 检查节流设置或减少更新频率
          </div>
        )}
      </div>
    </div>
  )
}

// Hook for using streaming performance monitoring
export const useStreamingPerformanceMonitor = () => {
  const [metrics, setMetrics] = useState<StreamingMetrics | null>(null)
  const [showMonitor, setShowMonitor] = useState(false)
  
  const startMonitoring = () => {
    setShowMonitor(true)
    setMetrics({
      updateCount: 0,
      averageUpdateInterval: 0,
      lastUpdateTime: 0,
      totalStreamingTime: 0,
      renderTime: 0
    })
  }
  
  const stopMonitoring = () => {
    setShowMonitor(false)
  }
  
  const toggleMonitor = () => {
    setShowMonitor(prev => !prev)
  }
  
  return {
    metrics,
    showMonitor,
    startMonitoring,
    stopMonitoring,
    toggleMonitor,
    setMetrics
  }
}

export default StreamingPerformanceMonitor
