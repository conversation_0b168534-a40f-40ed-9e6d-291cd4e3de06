'use client'

import React, { memo, useMemo, useCallback, useRef, useEffect } from 'react'
import { Sparkles, ChevronUp, ChevronDown } from 'lucide-react'
import SafeMarkdown from '@/components/ui/SafeMarkdown'
import ModernLoader from '@/components/ui/ModernLoader'
import { useSmartScrollFollow } from '@/hooks/useSmartScrollFollow'

interface OptimizedStructuredNotesProps {
  content: string
  streamingContent?: string
  isAnalyzing?: boolean
  isExpanded: boolean
  height: number
  onToggleExpanded: () => void
  onManualExpand: () => void
  onManualCollapse: () => void
  className?: string
}

// 使用memo优化渲染性能
const OptimizedStructuredNotes = memo<OptimizedStructuredNotesProps>(({
  content,
  streamingContent,
  isAnalyzing,
  isExpanded,
  height,
  onToggleExpanded,
  onManualExpand,
  onManualCollapse,
  className = ''
}) => {
  const dragHandleRef = useRef<HTMLDivElement>(null)
  
  // 智能滚动跟随
  const {
    containerRef: scrollContainerRef,
    scrollState,
    forceScrollToBottom,
    scrollToBottom
  } = useSmartScrollFollow({
    enabled: isExpanded && (!!streamingContent || isAnalyzing),
    threshold: 50,
    smoothScroll: true,
    debounceMs: 100
  })

  // 使用useMemo优化内容计算
  const displayContent = useMemo(() => {
    return streamingContent || content || ''
  }, [streamingContent, content])

  // 使用useMemo优化状态计算
  const noteStatus = useMemo(() => {
    if (isAnalyzing) return 'analyzing'
    if (streamingContent) return 'streaming'
    if (content) return 'completed'
    return 'empty'
  }, [isAnalyzing, streamingContent, content])

  // 优化的切换处理函数
  const handleToggle = useCallback(() => {
    if (isExpanded) {
      onManualCollapse()
    } else {
      onManualExpand()
    }
  }, [isExpanded, onManualExpand, onManualCollapse])

  // 监听内容变化，触发滚动跟随
  useEffect(() => {
    if (streamingContent && isExpanded && scrollState.shouldAutoFollow) {
      // 使用防抖延迟滚动，避免过于频繁的滚动
      const timeoutId = setTimeout(() => {
        scrollToBottom()
      }, 100)
      
      return () => clearTimeout(timeoutId)
    }
  }, [streamingContent, isExpanded, scrollState.shouldAutoFollow, scrollToBottom])

  // 渲染状态指示器
  const renderStatusIndicator = useCallback(() => {
    switch (noteStatus) {
      case 'analyzing':
        return (
          <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-gradient-to-r from-blue-100 to-purple-100 text-blue-800 border border-blue-200/50">
            🔄 分析中
          </span>
        )
      case 'streaming':
        return (
          <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-gradient-to-r from-green-100 to-blue-100 text-green-800 border border-green-200/50">
            ✨ 生成中
          </span>
        )
      case 'completed':
        return (
          <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-gradient-to-r from-gray-100 to-gray-200 text-gray-700 border border-gray-200/50">
            ✅ 完成
          </span>
        )
      default:
        return null
    }
  }, [noteStatus])

  // 渲染内容区域
  const renderContent = useCallback(() => {
    if (noteStatus === 'empty') {
      return (
        <div className="py-8 text-center">
          <div className="w-12 h-12 mx-auto bg-gray-100 rounded-full flex items-center justify-center mb-3">
            <Sparkles className="w-6 h-6 text-gray-400" />
          </div>
          <p className="text-gray-500 text-sm">暂无结构化笔记</p>
        </div>
      )
    }

    if (noteStatus === 'analyzing') {
      return (
        <div className="py-8">
          <ModernLoader variant="dots" size="md" text="正在生成结构化笔记..." className="text-center" />
        </div>
      )
    }

    return (
      <div className="ai-note-content bg-gradient-to-br from-slate-50/50 to-blue-50/30 rounded-xl p-4">
        <SafeMarkdown className="prose prose-sm max-w-none prose-headings:text-slate-800 prose-headings:font-semibold prose-p:text-slate-700 prose-p:leading-relaxed prose-ul:text-slate-700 prose-ol:text-slate-700 prose-li:my-1.5 prose-strong:text-slate-900 prose-code:text-blue-600 prose-code:bg-blue-50/80 prose-code:px-2 prose-code:py-0.5 prose-code:rounded-md prose-blockquote:border-l-4 prose-blockquote:border-blue-300 prose-blockquote:bg-blue-50/30 prose-blockquote:pl-4 prose-blockquote:py-2 prose-blockquote:rounded-r-lg">
          {displayContent}
        </SafeMarkdown>
        
        {/* 流式生成时的滚动指示器 */}
        {noteStatus === 'streaming' && !scrollState.isAtBottom && (
          <div className="fixed bottom-4 right-4 z-50">
            <button
              onClick={forceScrollToBottom}
              className="bg-blue-500 text-white p-2 rounded-full shadow-lg hover:bg-blue-600 transition-colors"
              title="滚动到底部"
            >
              <ChevronDown className="w-4 h-4" />
            </button>
          </div>
        )}
      </div>
    )
  }, [noteStatus, displayContent, scrollState.isAtBottom, forceScrollToBottom])

  return (
    <div
      className={`absolute top-4 left-4 right-4 bg-white backdrop-blur-lg rounded-3xl border border-slate-200/50 shadow-2xl shadow-slate-200/40 transition-all duration-300 ease-in-out ${className}`}
      style={{
        height: isExpanded ? `${height}%` : '60px',
        zIndex: 60,
        minHeight: '60px',
        maxHeight: '90%'
      }}
    >
      {/* 卡片头部 */}
      <div className="px-5 py-4 flex items-center justify-between border-b border-slate-200/50">
        <div className="flex items-center space-x-3">
          <div className="p-1.5 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg">
            <Sparkles className="w-4 h-4 text-white" />
          </div>
          <h3 className="font-semibold text-slate-800 text-sm">结构化笔记</h3>
          {renderStatusIndicator()}
        </div>

        {/* 折叠展开按钮 */}
        <button
          onClick={handleToggle}
          className="p-1.5 rounded-lg hover:bg-slate-100 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500/20"
          title={isExpanded ? "折叠" : "展开"}
        >
          {isExpanded ? (
            <ChevronUp className="w-4 h-4 text-slate-600" />
          ) : (
            <ChevronDown className="w-4 h-4 text-slate-600" />
          )}
        </button>
      </div>

      {/* 卡片内容 - 可滚动，支持折叠展开 */}
      {isExpanded && (
        <div 
          ref={scrollContainerRef}
          className="flex-1 overflow-y-auto px-4 py-4" 
          style={{ height: 'calc(100% - 80px)' }}
        >
          {renderContent()}
        </div>
      )}

      {/* 拖拽手柄 - 更圆润的设计 */}
      <div
        ref={dragHandleRef}
        className="absolute bottom-0 left-0 right-0 h-3 bg-gradient-to-r from-blue-500/20 to-purple-500/20 rounded-b-3xl cursor-ns-resize hover:from-blue-500/40 hover:to-purple-500/40 transition-all duration-200"
        title="拖拽调整高度"
      >
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-10 h-1.5 bg-slate-400/80 rounded-full"></div>
      </div>
    </div>
  )
})

OptimizedStructuredNotes.displayName = 'OptimizedStructuredNotes'

export default OptimizedStructuredNotes
