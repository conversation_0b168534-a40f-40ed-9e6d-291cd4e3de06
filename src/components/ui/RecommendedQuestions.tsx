'use client'

import React from 'react'
import { RecommendedQuestion } from '@/lib/store'
import { cn } from '@/lib/utils'

interface RecommendedQuestionsProps {
  questions: RecommendedQuestion[]
  onQuestionClick: (question: string) => void
  className?: string
}

const RecommendedQuestions: React.FC<RecommendedQuestionsProps> = ({
  questions,
  onQuestionClick,
  className
}) => {
  if (!questions || questions.length === 0) {
    return null
  }

  return (
    <div className={cn('space-y-3', className)}>
      {/* 问题卡片 - 横向排列，简洁设计 */}
      <div className="flex flex-col sm:flex-row gap-2">
        {questions.map((question) => {
          return (
            <button
              key={question.id}
              onClick={() => onQuestionClick(question.question)}
              className={cn(
                'group relative overflow-hidden rounded-2xl p-4 text-left transition-all duration-200 flex-1',
                'bg-white border border-gray-200 shadow-sm',
                'hover:shadow-md hover:border-gray-300 hover:scale-[1.02]',
                'active:scale-[0.98] focus:outline-none focus:ring-2 focus:ring-blue-500/20'
              )}
            >
              {/* 问题文本 */}
              <div className="w-full">
                <p className="text-sm font-medium text-gray-900 leading-relaxed group-hover:text-gray-800 transition-colors">
                  {question.question}
                </p>
              </div>

                {/* 箭头指示器 */}
                <div className="flex-shrink-0 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                  <div className="w-4 h-4 rounded-full bg-gray-100 flex items-center justify-center">
                    <svg className="w-2 h-2 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </div>
                </div>
              </div>
            </button>
          )
        })}
      </div>
    </div>
  )
}

export default RecommendedQuestions
