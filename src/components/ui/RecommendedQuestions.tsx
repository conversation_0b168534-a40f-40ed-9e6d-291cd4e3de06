'use client'

import React from 'react'
import { RecommendedQuestion } from '@/lib/store'
import { cn } from '@/lib/utils'

interface RecommendedQuestionsProps {
  questions: RecommendedQuestion[]
  onQuestionClick: (question: string) => void
  className?: string
}

const RecommendedQuestions: React.FC<RecommendedQuestionsProps> = ({
  questions,
  onQuestionClick,
  className
}) => {
  if (!questions || questions.length === 0) {
    return null
  }

  return (
    <div className={cn('space-y-3', className)}>
      <div className="flex flex-col sm:flex-row gap-2">
        {questions.map((question) => (
          <button
            key={question.id}
            onClick={() => onQuestionClick(question.question)}
            className={cn(
              'group relative overflow-hidden rounded-2xl p-4 text-left transition-all duration-200 flex-1',
              'bg-white border border-gray-200 shadow-sm',
              'hover:shadow-md hover:border-gray-300 hover:scale-[1.02]',
              'active:scale-[0.98] focus:outline-none focus:ring-2 focus:ring-blue-500/20'
            )}
          >
            <div className="w-full">
              <p className="text-sm font-medium text-gray-900 leading-relaxed group-hover:text-gray-800 transition-colors">
                {question.question}
              </p>
            </div>
          </button>
        ))}
      </div>
    </div>
  )
}

export default RecommendedQuestions
