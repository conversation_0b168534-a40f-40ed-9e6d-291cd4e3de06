'use client'

import React from 'react'
import { RecommendedQuestion } from '@/lib/store'
import { cn } from '@/lib/utils'

interface RecommendedQuestionsProps {
  questions: RecommendedQuestion[]
  onQuestionClick: (question: string) => void
  className?: string
}

const RecommendedQuestions: React.FC<RecommendedQuestionsProps> = ({
  questions,
  onQuestionClick,
  className
}) => {
  if (!questions || questions.length === 0) {
    return null
  }

  return (
    <div className={cn('flex flex-col sm:flex-row gap-2', className)}>
      {questions.map((question, index) => (
        <button
          key={question.id}
          onClick={() => onQuestionClick(question.question)}
          className={cn(
            'group relative overflow-hidden rounded-xl px-3 py-2 text-left transition-all duration-200 flex-1',
            'bg-white border border-gray-200 shadow-sm',
            'hover:shadow-md hover:border-gray-300 hover:scale-[1.02]',
            'active:scale-[0.98] focus:outline-none focus:ring-2 focus:ring-blue-500/20',
            'animate-fade-in-up'
          )}
          style={{
            animationDelay: `${index * 100}ms`,
            animationFillMode: 'both'
          }}
        >
          <div className="w-full">
            <p className="text-xs font-medium text-gray-900 leading-relaxed group-hover:text-gray-800 transition-colors">
              {question.question}
            </p>
          </div>
        </button>
      ))}
    </div>
  )
}

export default RecommendedQuestions
