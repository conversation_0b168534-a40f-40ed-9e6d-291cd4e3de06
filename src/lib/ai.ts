import OpenAI from 'openai'

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
})

export interface KnowledgeCard {
  title: string
  content: string
}

/**
 * 将网页内容转换为知识卡片
 */
export async function generateKnowledgeCards(content: string, url: string): Promise<KnowledgeCard[]> {
  const prompt = `
请将以下文章内容分解成多个独立的知识卡片。每张卡片应该包含一个明确的知识点或概念。

要求：
1. 每张卡片都应该是自包含的，读者无需其他上下文就能理解
2. 卡片标题简洁明了，体现核心概念
3. 卡片内容详细但精炼，包含关键信息和要点
4. 返回JSON数组格式，每个元素包含 "title" 和 "content" 字段
5. 至少生成3张卡片，最多10张卡片

文章内容：
${content}

请返回JSON格式的知识卡片数组：
`

  try {
    const response = await openai.chat.completions.create({
      model: "gpt-4o-mini",
      messages: [
        {
          role: "system",
          content: "你是一个专业的知识整理助手，擅长将复杂内容分解为清晰的知识卡片。"
        },
        {
          role: "user",
          content: prompt
        }
      ],
      temperature: 0.7,
      max_tokens: 2000
    })

    const result = response.choices[0]?.message?.content
    if (!result) {
      throw new Error('No response from OpenAI')
    }

    // 尝试解析JSON响应
    try {
      const cards = JSON.parse(result) as KnowledgeCard[]
      return cards.filter(card => card.title && card.content)
    } catch (parseError) {
      // 如果直接解析失败，尝试提取JSON部分
      const jsonMatch = result.match(/\[[\s\S]*\]/)
      if (jsonMatch) {
        const cards = JSON.parse(jsonMatch[0]) as KnowledgeCard[]
        return cards.filter(card => card.title && card.content)
      }
      throw new Error('Failed to parse knowledge cards from AI response')
    }
  } catch (error) {
    console.error('Error generating knowledge cards:', error)
    throw new Error('Failed to generate knowledge cards')
  }
}

/**
 * 将网页内容以流式方式转换为结构化笔记
 * @param content 文章原文
 * @returns OpenAI Stream
 */
export async function streamKnowledgeCards(content: string) {
  // 使用环境变量中的系统提示词，如果没有则使用默认值
  const systemPrompt = process.env.AI_SYSTEM_PROMPT || '你是一个专业的知识整理助手，擅长将复杂内容提炼成结构清晰、格式优美的Markdown笔记。'

  const basePrompt = process.env.STRUCTURED_NOTES_PROMPT || "请将以下文章内容提炼成一份结构化的Markdown笔记。文章内容："

  const prompt = `${basePrompt}
${content}

请开始生成Markdown格式的结构化笔记：`

  return openai.chat.completions.create({
    model: process.env.OPENAI_MODEL || 'gpt-4o-mini',
    messages: [
      {
        role: 'system',
        content: systemPrompt
      },
      {
        role: 'user',
        content: prompt
      }
    ],
    stream: true,
    temperature: parseFloat(process.env.OPENAI_TEMPERATURE || '0.5'),
    max_tokens: parseInt(process.env.OPENAI_MAX_TOKENS || '2000')
  })
}

/**
 * AI问答功能
 */
export async function askQuestion(question: string, context: string): Promise<string> {
  const systemPrompt = process.env.CHAT_SYSTEM_PROMPT || "你是一个专业的AI助手，基于给定的文章内容回答用户问题。请保持回答的准确性和相关性。"

  const prompt = `基于以下文章内容回答用户的问题。请提供准确、有用的答案。

文章内容：
${context}

用户问题：
${question}

请回答：`

  try {
    const response = await openai.chat.completions.create({
      model: process.env.OPENAI_MODEL || "gpt-4o-mini",
      messages: [
        {
          role: "system",
          content: systemPrompt
        },
        {
          role: "user",
          content: prompt
        }
      ],
      temperature: parseFloat(process.env.OPENAI_TEMPERATURE || '0.3'),
      max_tokens: parseInt(process.env.OPENAI_MAX_TOKENS || '1000')
    })

    return response.choices[0]?.message?.content || '抱歉，我无法回答这个问题。'
  } catch (error) {
    console.error('Error answering question:', error)
    throw new Error('Failed to get AI response')
  }
}