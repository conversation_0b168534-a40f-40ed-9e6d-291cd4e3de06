import type { Metadata } from "next";
import { Geist, <PERSON>ei<PERSON>_Mono } from "next/font/google";
import "./globals.css";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "沉淀 - AI记忆沉淀器",
  description: "专为信息过载的普通人设计的AI记忆沉淀器，让知识的获取、理解和沉淀过程合而为一",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <head>
        <link
          href="/tailwind.min.css"
          rel="stylesheet"
        />
        <link
          href="/font-awesome.min.css"
          rel="stylesheet"
        />
      </head>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        {children}
      </body>
    </html>
  );
}
