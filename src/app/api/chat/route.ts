import { NextRequest } from 'next/server'
import OpenAI from 'openai'

// 初始化OpenAI客户端
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
})

export async function POST(request: NextRequest) {
  try {
    const { message, context } = await request.json()
    
    if (!message) {
      return new Response('Missing message', { status: 400 })
    }
    
    // 检查是否有有效的OpenAI API密钥
    const apiKey = process.env.OPENAI_API_KEY
    if (!apiKey || apiKey === 'sk-test-key-for-development' || apiKey === 'your_openai_api_key_here') {
      // 返回模拟的流式响应
      const stream = new ReadableStream({
        start(controller) {
          const mockResponse = `我收到了您的问题："${message}"\n\n这是一个演示模式的回复，因为没有配置有效的OpenAI API密钥。在实际使用中，AI会基于以下上下文进行智能回答：\n\n**上下文信息：**\n- 原文内容长度：${context?.originalContent?.length || 0} 字符\n- AI笔记长度：${context?.aiNote?.length || 0} 字符\n\n💡 要获得真正的AI对话体验，请配置有效的OpenAI API密钥。`
          
          // 模拟流式输出
          const words = mockResponse.split('')
          let index = 0
          
          const sendNextChunk = () => {
            if (index < words.length) {
              const chunk = words.slice(index, index + 3).join('')
              controller.enqueue(new TextEncoder().encode(`data: ${JSON.stringify({ content: chunk })}\n\n`))
              index += 3
              setTimeout(sendNextChunk, 30)
            } else {
              controller.enqueue(new TextEncoder().encode('data: [DONE]\n\n'))
              controller.close()
            }
          }
          
          setTimeout(sendNextChunk, 100)
        }
      })
      
      return new Response(stream, {
        headers: {
          'Content-Type': 'text/plain; charset=utf-8',
          'Transfer-Encoding': 'chunked',
          'Cache-Control': 'no-cache',
          'Connection': 'keep-alive',
        },
      })
    }

    // 构建聊天上下文
    const baseSystemPrompt = process.env.CHAT_SYSTEM_PROMPT || '你是一个专业的AI助手，专门帮助用户分析和理解内容。请基于提供的原文内容和AI笔记回答用户的问题，提供深入、有用的见解。回答要简洁明了，使用Markdown格式。'

    const systemPrompt = `${baseSystemPrompt}

当前上下文：
- 原文内容：${context?.originalContent || '无'}
- AI笔记：${context?.aiNote || '无'}`

    // 创建流式响应
    const stream = await openai.chat.completions.create({
      model: process.env.OPENAI_MODEL || 'gpt-4o-mini',
      messages: [
        {
          role: 'system',
          content: systemPrompt
        },
        {
          role: 'user',
          content: message
        }
      ],
      stream: true,
      max_tokens: parseInt(process.env.OPENAI_MAX_TOKENS || '1000'),
      temperature: parseFloat(process.env.OPENAI_TEMPERATURE || '0.7')
    })

    const readableStream = new ReadableStream({
      async start(controller) {
        try {
          for await (const chunk of stream) {
            const content = chunk.choices[0]?.delta?.content || ''
            if (content) {
              controller.enqueue(new TextEncoder().encode(`data: ${JSON.stringify({ content })}\n\n`))
            }
          }
          controller.enqueue(new TextEncoder().encode('data: [DONE]\n\n'))
          controller.close()
        } catch (error) {
          console.error('Streaming error:', error)
          controller.error(error)
        }
      }
    })

    return new Response(readableStream, {
      headers: {
        'Content-Type': 'text/plain; charset=utf-8',
        'Transfer-Encoding': 'chunked',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
      },
    })

  } catch (error) {
    console.error('Chat API error:', error)
    return new Response(
      JSON.stringify({ 
        error: error instanceof Error ? error.message : '处理失败',
        success: false 
      }),
      { 
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      }
    )
  }
}
