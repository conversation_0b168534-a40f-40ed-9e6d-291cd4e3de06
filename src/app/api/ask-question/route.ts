import { NextRequest, NextResponse } from 'next/server'
import { askQuestion } from '@/lib/ai'

export async function POST(request: NextRequest) {
  try {
    const { question, context } = await request.json()

    if (!question || !context) {
      return NextResponse.json(
        { error: 'Question and context are required' },
        { status: 400 }
      )
    }

    const answer = await askQuestion(question, context)

    return NextResponse.json({ answer })

  } catch (error) {
    console.error('Error answering question:', error)
    return NextResponse.json(
      { error: 'Failed to get answer' },
      { status: 500 }
    )
  }
} 