---
description: 
globs: 
alwaysApply: true
---
---
description: 智能知识卡片生成器项目开发规范
globs: ["**/*.{ts,tsx,js,jsx,md,json}"]
alwaysApply: true
---

# 项目概述

这是一个基于 Next.js 的智能知识卡片生成器，可以将网页内容转换为结构化的知识卡片，并提供 AI 问答功能。

## 技术栈

- **前端框架**: Next.js 15 (App Router) + React
- **UI 组件**: shadcn/ui + Tailwind CSS
- **数据库**: SQLite + Prisma ORM
- **网页抓取**: @mozilla/readability + Playwright
- **AI 服务**: OpenAI GPT-4o-mini
- **类型安全**: TypeScript

## 项目结构

```
src/
├── app/
│   ├── api/           # API 路由
│   │   ├── process-url/    # 处理 URL
│   │   ├── save-card/      # 保存卡片
│   │   ├── ask-question/   # AI 问答
│   │   └── cards/          # 获取卡片列表
│   ├── globals.css    # 全局样式
│   └── page.tsx      # 主页面
├── components/ui/     # shadcn/ui 组件
├── lib/              # 工具库
│   ├── prisma.ts     # 数据库客户端
│   ├── scraper.ts    # 网页抓取工具
│   └── ai.ts         # AI 服务
└── generated/        # Prisma 生成的类型
```

## 开发规范

### 代码风格
- 使用 TypeScript 严格模式
- 遵循 ESLint + Prettier 配置
- 组件命名使用 PascalCase
- 函数和变量使用 camelCase
- 常量使用 UPPER_SNAKE_CASE

### API 设计
- 使用 Next.js App Router API 路由
- 统一错误处理和响应格式
- 输入验证和类型检查
- 适当的 HTTP 状态码

### 数据库操作
- 使用 Prisma Client 进行数据库操作
- 统一的错误处理
- 适当的数据验证

### 前端规范
- 使用 "use client" 指令标记客户端组件
- 适当的 loading 状态和错误处理
- 响应式设计（移动端适配）
- 无障碍性考虑

## 环境配置

创建 `.env` 文件并配置以下变量：
```
DATABASE_URL="file:./dev.db"
OPENAI_API_KEY="your-openai-api-key"
```

## 常用命令

```bash
# 开发服务器
npm run dev

# 数据库操作
npx prisma db push      # 推送 schema 变更
npx prisma generate     # 生成 Prisma Client
npx prisma studio       # 打开数据库管理界面

# 构建和部署
npm run build
npm start
```

## 功能模块

1. **URL 处理**: 智能抓取网页内容，支持静态和动态页面
2. **AI 卡片生成**: 使用 OpenAI API 将内容转换为知识卡片
3. **卡片管理**: 保存、查看、管理知识卡片
4. **AI 问答**: 基于原文内容的智能问答
5. **缓存机制**: 避免重复处理相同 URL
