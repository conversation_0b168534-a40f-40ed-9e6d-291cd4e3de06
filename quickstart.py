#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
知识卡片项目快速启动脚本（简化版）
Quick Start Script for Knowledge Cards Project (Simplified)
"""

import os
import sys
import subprocess
import platform
from pathlib import Path

def run_cmd(cmd, description=""):
    """执行命令并显示结果"""
    if description:
        print(f"🔄 {description}...")
    
    try:
        result = subprocess.run(cmd, shell=True, check=True, capture_output=True, text=True)
        if description:
            print(f"✅ {description} 完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} 失败: {e.stderr}")
        return False

def main():
    """主函数"""
    print("🚀 知识卡片项目快速启动")
    print("=" * 40)
    
    # 检查基本环境
    if not Path("package.json").exists():
        print("❌ 请在项目根目录运行此脚本！")
        sys.exit(1)
    
    # 安装依赖（如果需要）
    if not Path("node_modules").exists():
        print("📦 安装项目依赖...")
        if not run_cmd("npm install", "依赖安装"):
            sys.exit(1)
    else:
        print("✅ 依赖已安装")
    
    # 创建环境变量文件（如果不存在）
    if not Path(".env").exists():
        print("📝 创建环境变量文件...")
        env_content = '''DATABASE_URL="file:./dev.db"
OPENAI_API_KEY="your-openai-api-key-here"
NODE_ENV="development"
'''
        with open(".env", "w") as f:
            f.write(env_content)
        print("✅ .env 文件已创建")
        print("⚠️  请配置您的 OPENAI_API_KEY")
    
    # 初始化数据库（如果需要）
    if Path("src/lib/prisma.ts").exists() and not Path("dev.db").exists():
        run_cmd("npx prisma generate", "生成 Prisma Client")
        run_cmd("npx prisma db push", "初始化数据库")
    
    # 启动开发服务器
    print("\n🚀 启动开发服务器...")
    print("📍 服务器地址: http://localhost:3000")
    print("⌨️  按 Ctrl+C 停止服务器\n")
    
    try:
        subprocess.run("npm run dev", shell=True)
    except KeyboardInterrupt:
        print("\n👋 服务器已停止")

if __name__ == "__main__":
    main() 